import React from "react";

export const ContactUsSection = (): JSX.Element => {
  return (
    <section className="w-full py-12 bg-gray-50">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center">
          <div className="flex items-center justify-center mb-6">
            <span className="text-[#190c0c] font-medium text-lg">Contact us</span>
            <span className="ml-2 text-[#190c0c]">▼</span>
          </div>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-sm mb-8">
            <a href="#opening-hours" className="text-[#935151] hover:text-[#dd3333] transition-colors">
              Opening Hours
            </a>
            <a href="#how-it-works" className="text-[#935151] hover:text-[#dd3333] transition-colors">
              How It Works
            </a>
          </div>

          {/* Social Media Icons */}
          <div className="flex items-center justify-center gap-4 mb-6">
            <a href="#twitter" className="text-[#1da1f2] hover:opacity-80 transition-opacity">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
            </a>
            <a href="#facebook" className="text-[#1877f2] hover:opacity-80 transition-opacity">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </a>
            <a href="#instagram" className="text-[#e4405f] hover:opacity-80 transition-opacity">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.875-1.387-2.026-1.387-3.323s.49-2.448 1.297-3.323c.875-.897 2.026-1.387 3.323-1.387s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"/>
              </svg>
            </a>
          </div>

          <p className="text-[#935151] text-sm">
            ©2024 Food Delivery. All rights reserved.
          </p>
        </div>
      </div>
    </section>
  );
};
