# EmailJS Configuration (Required for approval emails)
# Get these values from https://www.emailjs.com/

# Your EmailJS Service ID (e.g., service_abc123)
VITE_EMAILJS_SERVICE_ID=your_service_id_here

# Your EmailJS Template ID (e.g., template_xyz789)
VITE_EMAILJS_TEMPLATE_ID=your_template_id_here

# Your EmailJS Public Key (from Account settings)
VITE_EMAILJS_PUBLIC_KEY=your_public_key_here

# Firebase Configuration (REQUIRED for approval functionality)
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
