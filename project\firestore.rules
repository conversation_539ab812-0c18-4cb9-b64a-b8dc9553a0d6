rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if user owns the document
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }
    
    // Helper function to check user role
    function hasRole(role) {
      return request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == role;
    }
    
    // USERS COLLECTION - Enhanced CRUD Rules
    match /users/{userId} {
      // Read: Users can read their own data, admins can read all
      allow read: if isOwner(userId) || isAdmin();
      
      // Create: Anyone can create their own user document during signup
      allow create: if isAuthenticated() && 
        request.auth.uid == userId &&
        request.resource.data.keys().hasAll(['email', 'name', 'role']) &&
        request.resource.data.email == request.auth.token.email;
      
      // Update: Users can update their own data, admins can update any user
      allow update: if isOwner(userId) || isAdmin();
      
      // Delete: Only admins can delete users
      allow delete: if isAdmin();
    }
    
    // ADMIN DASHBOARD - Full access for admins
    match /{document=**} {
      allow read, write: if isAdmin();
    }
    
    // RESTAURANTS COLLECTION
    match /restaurants/{restaurantId} {
      // Read: Public read for browsing restaurants
      allow read: if true;
      
      // Create: Restaurant owners and admins can create restaurants
      allow create: if isAuthenticated() && 
        (hasRole('restaurant_owner') || isAdmin()) &&
        request.resource.data.keys().hasAll(['name', 'email', 'ownerId']);
      
      // Update: Restaurant owners can update their own restaurants, admins can update any
      allow update: if isAuthenticated() && 
        (resource.data.ownerId == request.auth.uid || isAdmin());
      
      // Delete: Only admins can delete restaurants
      allow delete: if isAdmin();
    }
    
    // ORDERS COLLECTION
    match /orders/{orderId} {
      // Read: Customers, restaurant owners, drivers, and admins can read relevant orders
      allow read: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid ||
         resource.data.restaurantOwnerId == request.auth.uid ||
         resource.data.driverId == request.auth.uid ||
         isAdmin());
      
      // Create: Authenticated users can create orders
      allow create: if isAuthenticated() &&
        request.resource.data.userId == request.auth.uid;
      
      // Update: Order participants and admins can update orders
      allow update: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid ||
         resource.data.restaurantOwnerId == request.auth.uid ||
         resource.data.driverId == request.auth.uid ||
         isAdmin());
      
      // Delete: Only admins can delete orders
      allow delete: if isAdmin();
    }
    
    // DRIVERS COLLECTION
    match /drivers/{driverId} {
      // Read: Drivers can read their own data, admins can read all
      allow read: if isOwner(driverId) || isAdmin();
      
      // Create: Drivers and admins can create driver profiles
      allow create: if isAuthenticated() && 
        (request.auth.uid == driverId || isAdmin()) &&
        request.resource.data.keys().hasAll(['name', 'email', 'userId']);
      
      // Update: Drivers can update their own data, admins can update any
      allow update: if isOwner(driverId) || isAdmin();
      
      // Delete: Only admins can delete driver profiles
      allow delete: if isAdmin();
    }
    
    // PARTNER REQUESTS COLLECTION
    match /partnerRequests/{requestId} {
      // Read: Admins can read all requests, users can read their own
      allow read: if isAdmin() || 
        (isAuthenticated() && resource.data.userId == request.auth.uid);
      
      // Create: Authenticated users can create partner requests
      allow create: if isAuthenticated() &&
        request.resource.data.userId == request.auth.uid;
      
      // Update: Only admins can update partner requests (approve/deny)
      allow update: if isAdmin();
      
      // Delete: Admins can delete requests
      allow delete: if isAdmin();
    }
    
    // ANALYTICS COLLECTION (Admin only)
    match /analytics/{document} {
      allow read, write: if isAdmin();
    }
    
    // SYSTEM SETTINGS COLLECTION (Admin only)
    match /systemSettings/{document} {
      allow read, write: if isAdmin();
    }
    
    // NOTIFICATIONS COLLECTION
    match /notifications/{notificationId} {
      // Read: Users can read their own notifications, admins can read all
      allow read: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid || isAdmin());
      
      // Create: System can create notifications (admins)
      allow create: if isAdmin();
      
      // Update: Users can mark their notifications as read, admins can update any
      allow update: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid || isAdmin());
      
      // Delete: Users can delete their own notifications, admins can delete any
      allow delete: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid || isAdmin());
    }
    
    // AUDIT LOGS COLLECTION (Admin only)
    match /auditLogs/{logId} {
      allow read, write: if isAdmin();
    }
  }
}
