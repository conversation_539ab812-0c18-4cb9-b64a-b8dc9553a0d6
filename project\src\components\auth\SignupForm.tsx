import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';

import { useAuth, UserRole } from '../../contexts/AuthContext';
import { EmailVerificationModal } from './EmailVerificationModal';
import { Eye, EyeOff, Loader2 } from 'lucide-react';

interface SignupFormProps {
  onToggleMode: () => void;
}

export const SignupForm: React.FC<SignupFormProps> = ({ onToggleMode }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showVerificationModal, setShowVerificationModal] = useState(false);

  // Log modal state changes
  React.useEffect(() => {
    if (showVerificationModal) {
      console.log('✅ Email verification modal is now visible');
    }
  }, [showVerificationModal]);
  const { registerCustomer, isLoading } = useAuth();
  const navigate = useNavigate();

  const getRedirectPath = (role: string): string => {
    switch (role) {
      case 'admin':
        return '/admin';
      case 'customer':
        return '/dashboard';
      case 'restaurant_owner':
        return '/restaurant';
      case 'delivery_rider':
        return '/delivery';
      default:
        return '/dashboard';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!formData.name || !formData.email || !formData.password) {
      setError('Please fill in all fields');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters long');
      return;
    }

    alert('Form submitted! Starting registration...');
    console.log('🔄 Starting customer registration from signup form:', formData.email);
    console.log('🔄 Current modal state before registration:', showVerificationModal);

    try {
      console.log('🔄 Attempting customer registration...');
      const registrationSuccess = await registerCustomer(formData.email, formData.password, formData.name);
      console.log('🔄 Registration result:', registrationSuccess);

      if (registrationSuccess) {
        console.log('✅ Customer registration successful, showing verification modal');
        setError('');

        // Show success message immediately
        const successMessage = `🎉 Account created successfully! Please check your email (${formData.email}) and click the verification link before logging in.`;
        setSuccess(successMessage);

        // Debug: Alert to make sure this code runs
        alert('SUCCESS: Account created! Check your email for verification.');

        // Clear form
        setFormData({ name: '', email: '', password: '', confirmPassword: '' });

        // Also try to show the modal
        setTimeout(() => {
          setShowVerificationModal(true);
          console.log('✅ Email verification modal triggered');
        }, 100);
      } else {
        console.log('❌ Customer registration failed - registerCustomer returned false');
        alert('ERROR: Registration failed');
        setError('Failed to create account. Please try again.');
      }
    } catch (error: any) {
      console.error('❌ Registration error caught in signup form:', error);
      alert(`ERROR CAUGHT: ${error.message}`);

      // Check if it's a specific error that still means account was created
      if (error.message && (
        error.message.includes('email verification') ||
        error.message.includes('email already in use') ||
        error.message.includes('User created successfully')
      )) {
        console.log('⚠️ Account likely created, showing verification message');
        setError('');
        setSuccess(`🎉 Account created! Please check your email (${formData.email}) and click the verification link before logging in.`);
        alert('Account created despite error! Check your email.');

        // Clear form
        setFormData({ name: '', email: '', password: '', confirmPassword: '' });

        setTimeout(() => {
          setShowVerificationModal(true);
        }, 100);
      } else {
        setError(`Failed to create account: ${error.message || 'Please try again.'}`);
      }
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCloseModal = () => {
    setShowVerificationModal(false);
    // Clear the form when modal is closed
    setFormData({
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    });
  };

  const handleSwitchToLogin = () => {
    setShowVerificationModal(false);
    // Clear the form
    setFormData({
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    });
    onToggleMode();
  };

  return (
    <>
      <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">Create Customer Account</CardTitle>
        <CardDescription className="text-center">
          Join Grubz as a customer and start ordering delicious food
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Full Name</Label>
            <Input
              id="name"
              type="text"
              placeholder="Enter your full name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              required
            />
          </div>


          
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Create a password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                required
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirm Password</Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                required
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {error && (
            <div className="text-red-500 text-sm text-center">{error}</div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-green-800 text-sm text-center font-medium">
              {success}
            </div>
          )}

          <Button type="submit" className="w-full bg-[#dd3333] hover:bg-[#c52e2e]" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating Account...
              </>
            ) : (
              'Create Account'
            )}
          </Button>




        </form>

        <div className="mt-6 text-center space-y-4">
          <p className="text-sm text-gray-600">
            Already have an account?{' '}
            <button
              onClick={onToggleMode}
              className="text-[#dd3333] hover:underline font-medium"
            >
              Sign in
            </button>
          </p>


        </div>
      </CardContent>
      </Card>

      {/* Email Verification Modal */}
      <EmailVerificationModal
        isOpen={showVerificationModal}
        onClose={handleCloseModal}
        userEmail={formData.email}
        userPassword={formData.password}
        onSwitchToLogin={handleSwitchToLogin}
      />
    </>
  );
};